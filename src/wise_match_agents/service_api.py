"""
面试分析服务API

提供基于LangGraph的并行面试分析工作流API服务
"""

import os
import asyncio
import json
import logging
import time
import random
from contextlib import asynccontextmanager
from concurrent.futures import ThreadPoolExecutor
from typing import Any, Dict, List, Optional

from attr import dataclass
from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field, ValidationError

from wise_match_agents.report_service.api_monitor import get_api_monitor
from wise_match_agents.report_service.model_manager import cleanup_model_managers
from wise_match_agents.report_service.workflow import InterviewAnalysisWorkflow
from wise_match_agents.config.sql_config import get_app_config

# 获取日志记录器
logger = logging.getLogger(__name__)

# 使用 attr dataclass 定义数据结构
@dataclass
class AnalysisRequest:
    """分析请求数据结构"""
    grouped_history: str = Field(..., description="分组的对话历史JSON字符串")
    examine_data: str = Field(..., description="考核数据JSON字符串")
    jd_data: str = Field(..., description="职位描述字符串")
    job_name: str = Field(..., description="职位名称")

@dataclass
class AnalysisResponse:
    """分析响应数据结构"""
    success: bool = Field(default=True, description="请求是否成功")
    data: Optional[Dict[str, Any]] = Field(default=None, description="评估结果")
    message: str = Field(default="", description="响应消息")
    processing_time: float = Field(default=0.0, description="处理时间(秒)")

# Pydantic模型用于FastAPI
class InterviewAnalysisRequest(BaseModel):
    """面试分析请求模型"""
    grouped_history: str = Field(..., description="分组的对话历史JSON字符串")
    examine_data: str = Field(..., description="考核数据JSON字符串")
    jd_data: str = Field(default="", description="职位描述字符串")
    job_name: str = Field(..., description="职位名称")

class InterviewAnalysisResponse(BaseModel):
    """面试分析响应模型"""
    success: bool = Field(default=True, description="请求是否成功")
    data: Optional[Dict[str, Any]] = Field(default=None, description="评估结果")
    message: str = Field(default="", description="响应消息")
    processing_time: float = Field(default=0.0, description="处理时间(秒)")

# 全局变量
workflow_instance: Optional[InterviewAnalysisWorkflow] = None
executor: Optional[ThreadPoolExecutor] = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global workflow_instance, executor

    # 启动时初始化
    logger.info("🚀 初始化面试分析服务...")

    # 获取应用配置
    env = os.getenv("WISE_MATCH_ENV", "dev")
    app_config = get_app_config(env)
    max_workers = app_config["app"]["max_workers"]

    workflow_instance = InterviewAnalysisWorkflow(git_env=env)

    # 根据配置调整线程池大小
    executor = ThreadPoolExecutor(max_workers=max_workers)
    logger.info(f"✅ 服务初始化完成 - 最大工作线程数: {max_workers}")

    yield

    # 关闭时清理
    logger.info("🔄 关闭面试分析服务...")
    if executor:
        executor.shutdown(wait=True)

    # 清理HTTP连接池资源
    cleanup_model_managers()
    logger.info("✅ 服务已关闭")

def create_app() -> FastAPI:
    """创建FastAPI应用实例"""
    import os

    # 获取应用配置
    env = os.getenv("WISE_MATCH_ENV", "dev")
    app_config = get_app_config(env)

    # 创建FastAPI应用
    app = FastAPI(
        title="面试分析服务",
        description="基于LangGraph的并行面试分析工作流API服务",
        version="0.1.0",
        lifespan=lifespan,
        debug=app_config["app"]["debug"]
    )

    # 添加HTTP升级处理中间件
    @app.middleware("http")
    async def reject_http_upgrade(request: Request, call_next):
        """拒绝HTTP升级请求，强制使用HTTP/1.1"""
        if request.headers.get("upgrade") or request.headers.get("connection", "").lower().find("upgrade") != -1:
            logger.warning(f"拒绝HTTP升级请求: {request.headers}")
            return JSONResponse(
                status_code=400,
                content={"error": "HTTP升级不支持，请使用HTTP/1.1"}
            )
        return await call_next(request)

    # 添加CORS中间件支持跨域
    app.add_middleware(
        CORSMiddleware,
        allow_origins=app_config["app"]["cors_origins"],
        allow_credentials=False,
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        allow_headers=["*"],
    )

    return app


# 创建应用实例
app = create_app()

# 添加验证错误处理器
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """处理请求验证错误"""
    logger.error(f"❌ 请求验证失败: {exc.errors()}")

    # 获取请求体
    try:
        body = await request.body()
        logger.error(f"请求体长度: {len(body)} bytes")
        logger.error(f"Content-Type: {request.headers.get('content-type', 'Not set')}")
        logger.error(f"请求体前100字符: {body[:100]}")

        # 尝试解析JSON
        if body:
            try:
                import json
                parsed = json.loads(body)
                logger.error(f"JSON解析成功，包含字段: {list(parsed.keys()) if isinstance(parsed, dict) else 'Not a dict'}")
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析失败: {e}")
        else:
            logger.error("请求体为空")

    except Exception as e:
        logger.error(f"读取请求体失败: {e}")

    error_details = []
    for error in exc.errors():
        error_details.append({
            "field": " -> ".join(str(loc) for loc in error["loc"]),
            "message": error["msg"],
            "type": error["type"]
        })

    return JSONResponse(
        status_code=422,
        content={
            "success": False,
            "message": "请求数据验证失败",
            "errors": error_details,
            "detail": str(exc)
        }
    )

def process_interview_analysis(
    grouped_history_str: str,
    examine_data_str: str,
    jd_data: str,
    job_name: str
) -> Dict[str, Any]:
    """同步处理面试分析的函数 - 添加3次重试兜底逻辑"""
    max_retries = 3
    last_exception = None

    for attempt in range(max_retries):
        try:
            # 解析JSON字符串
            grouped_history = json.loads(grouped_history_str)
            examine_data = json.loads(examine_data_str)
            
            # 执行工作流
            result = workflow_instance.run(grouped_history, examine_data, jd_data, job_name)
            return result
            
        except json.JSONDecodeError as e:
            last_exception = e
            error_msg = f"JSON解析失败(尝试 {attempt + 1}/{max_retries}): {str(e)}"
            logger.error(error_msg)
            print(error_msg)
            # JSON解析错误通常不需要重试，直接抛出
            raise ValueError(f"JSON解析失败: {str(e)}")
            
        except Exception as e:
            last_exception = e
            error_msg = f"工作流执行失败(尝试 {attempt + 1}/{max_retries}): {str(e)}"
            logger.error(error_msg)
            print(error_msg)

            # 如果是最后一次重试，抛出原始异常
            # 使用指数退避策略，避免立即重试
            if attempt < max_retries - 1:
                wait_time = (2 ** attempt) + random.uniform(0.1, 0.5)
                logger.info(f"⏳ 等待 {wait_time:.2f} 秒后重试...")
                time.sleep(wait_time)
            else:
                # 最后一次尝试失败，抛出异常
                logger.error(f"❌ 所有重试尝试均失败，最终错误: {str(last_exception)}")
                raise RuntimeError(f"工作流执行失败: {str(last_exception)}")


@app.post("/report/analyze", response_model=InterviewAnalysisResponse)
async def analyze_interview(request: InterviewAnalysisRequest) -> InterviewAnalysisResponse:
    """面试分析接口"""
    start_time = time.time()

    try:
        logger.info("📥 收到面试分析请求")
        logger.info(f"请求数据: grouped_history长度={len(request.grouped_history)}, examine_data长度={len(request.examine_data)}, jd_data长度={len(request.jd_data)}, job_name={request.job_name}")

        # 获取线程池执行器
        thread_executor = get_executor()

        # 异步执行工作流（在线程池中运行同步代码）
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            thread_executor,
            process_interview_analysis,
            request.grouped_history,
            request.examine_data,
            request.jd_data,
            request.job_name
        )

        processing_time = time.time() - start_time

        # 打印API统计信息
        monitor = get_api_monitor()
        monitor.log_stats()

        logger.info(f"✅ 分析完成，耗时 {processing_time:.2f} 秒")

        return InterviewAnalysisResponse(
            success=True,
            data=result,
            message="分析完成",
            processing_time=processing_time
        )

    except ValueError as e:
        processing_time = time.time() - start_time
        logger.error(f"❌ 参数错误: {str(e)}")
        return InterviewAnalysisResponse(
            success=False,
            data=None,
            message=f"参数错误: {str(e)}",
            processing_time=processing_time
        )

    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(f"❌ 服务器错误: {str(e)}")
        return InterviewAnalysisResponse(
            success=False,
            data=None,
            message=f"服务器错误: {str(e)}",
            processing_time=processing_time
        )

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {
        "status": "healthy",
        "service": "服务健康",
        "timestamp": time.time()
    }

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "面试分析服务运行中",
        "docs": "/docs",
        "health": "/health"
    }


def get_workflow_instance() -> InterviewAnalysisWorkflow:
    """获取工作流实例"""
    if workflow_instance is None:
        raise HTTPException(
            status_code=503,
            detail="服务正在初始化中，请稍后重试"
        )
    return workflow_instance


def get_executor() -> ThreadPoolExecutor:
    """获取线程池执行器"""
    if executor is None:
        raise HTTPException(
            status_code=503,
            detail="服务正在初始化中，请稍后重试"
        )
    return executor
