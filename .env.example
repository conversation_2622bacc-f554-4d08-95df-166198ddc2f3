# 环境配置示例文件
# 复制此文件为 .env 或 .env.prod 并修改相应的值
#
# 配置优先级说明：
# 1. 如果设置了完整的数据库环境变量 (DB_HOST, DB_USER, DB_PASSWORD, DB_NAME)，
#    系统将优先使用环境变量配置，不再读取 config/database_*.yaml 文件
# 2. 如果环境变量不完整，系统会回退到读取 YAML 配置文件
# 3. 推荐使用环境变量配置，更安全且便于部署

# =============================================================================
# 应用环境配置
# =============================================================================

# 环境标识 (dev/prod)
WISE_MATCH_ENV=dev

# 服务配置
APP_HOST=0.0.0.0
APP_PORT=8000
APP_NAME=wise-match-agents
APP_VERSION=0.1.0

# 日志配置
LOG_LEVEL=INFO

# 工作线程配置
MAX_WORKERS=10

# CORS配置 (逗号分隔多个域名)
CORS_ORIGINS=*

# =============================================================================
# 数据库配置
# =============================================================================

# 数据库连接信息 (必需，优先使用环境变量配置)
# 如果这些环境变量都设置了，将不再读取 config/database_*.yaml 文件
DB_HOST=your-database-host
DB_PORT=3306
DB_USER=your-username
DB_PASSWORD=your-password
DB_NAME=your-database-name
DB_CHARSET=utf8mb4

# 数据库连接池配置
DB_MIN_CACHED=5
DB_MAX_CACHED=30
DB_MAX_CONNECTIONS=50
DB_MAX_USAGE=1000

# =============================================================================
# AI模型配置
# =============================================================================

# OpenAI API配置
OPENAI_API_KEY=your-openai-api-key
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-3.5-turbo

# DashScope配置 (阿里云)
DASHSCOPE_API_KEY=your-dashscope-api-key

# LangSmith配置 (可选)
LANGCHAIN_TRACING_V2=false
LANGCHAIN_API_KEY=your-langsmith-api-key
LANGCHAIN_PROJECT=wise-match-agents

# =============================================================================
# 其他服务配置
# =============================================================================

# Redis配置 (如果使用)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090

# 安全配置
SECRET_KEY=your-secret-key
JWT_SECRET_KEY=your-jwt-secret-key
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=1440

# =============================================================================
# 开发环境特定配置
# =============================================================================

# 热重载 (仅开发环境)
RELOAD=true

# 调试模式
DEBUG=true

# 测试数据库配置 (仅测试环境)
TEST_DB_HOST=localhost
TEST_DB_PORT=3306
TEST_DB_USER=test_user
TEST_DB_PASSWORD=test_password
TEST_DB_NAME=test_database

# =============================================================================
# 生产环境特定配置
# =============================================================================

# SSL配置
SSL_KEYFILE=/path/to/ssl/keyfile.pem
SSL_CERTFILE=/path/to/ssl/certfile.pem

# 访问限制
ALLOWED_HOSTS=your-domain.com,api.your-domain.com

# 性能配置
UVLOOP_ENABLED=true
HTTPTOOLS_ENABLED=true

# 监控和日志
SENTRY_DSN=your-sentry-dsn
LOG_FILE=/var/log/wise-match-agents/app.log
LOG_MAX_SIZE=10MB
LOG_BACKUP_COUNT=5
